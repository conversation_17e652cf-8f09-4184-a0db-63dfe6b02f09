defmodule Repobot.Base64Content do
  @moduledoc """
  A custom Ecto type for handling base64-encoded content.
  This type automatically handles encoding/decoding of content to/from base64
  when loading from or dumping to the database.
  """
  use Ecto.Type

  def type, do: :text

  # When loading from the database, decode if base64
  def cast(nil), do: {:ok, nil}
  def cast(content) when is_binary(content), do: {:ok, content}
  def cast(_), do: :error

  # When loading from the database, try to decode base64
  def load(nil), do: {:ok, nil}

  def load(content) when is_binary(content) do
    # Remove newlines before decoding (GitHub API and our migration include newlines)
    cleaned_content = String.replace(content, "\n", "")

    case Base.decode64(cleaned_content, padding: false) do
      {:ok, decoded} -> {:ok, decoded}
      # If not base64, return as-is
      :error -> {:ok, content}
    end
  end

  # When writing to the database, encode as base64
  def dump(nil), do: {:ok, nil}

  def dump(content) when is_binary(content) do
    encoded = Base.encode64(content, padding: false)
    {:ok, encoded}
  end

  def dump(_), do: :error
end
